const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { hasDiscordPermission } = require('../../utils/permissions');
const { runHelpCommand } = require('../../utils/commandProcessor');
const { findUser } = require('../../handlers/finder');
const { ensureGuildData } = require('../../database');
const { createPagination, createConfirmation } = require('../../core/buttons');
const guildCache = require('../../database/cache/models/guild');
const config = require('../../config/setup');

const SWEAR_WORDS = [
  { base: 'fuck', variations: ['fuck', 'f*ck', 'f@ck', 'f.u.c.k', 'fuq', 'fuk', 'fck'] },
  { base: 'shit', variations: ['shit', 'sh*t', 'sh!t', 'sh1t', 'sht'] },
  { base: 'bitch', variations: ['bitch', 'b*tch', 'b!tch', 'b1tch', 'btch'] },
  { base: 'ass', variations: ['ass', 'a$$', 'a.ss', 'a*s'] },
  { base: 'bastard', variations: ['bastard', 'b*stard', 'bastrd'] },
  { base: 'dick', variations: ['dick', 'd*ck', 'd!ck', 'd1ck', 'dck', 'dik', 'dikk', 'penis', 'pen1s'] },
  { base: 'pussy', variations: ['pussy', 'p*ssy', 'p!ssy', 'psy'] },
  { base: 'cunt', variations: ['cunt', 'c*nt', 'c!nt', 'cnt'] },
  { base: 'slut', variations: ['slut', 's*ut', 's!ut', 'slt'] },
  { base: 'whore', variations: ['whore', 'wh*re', 'wh!re', 'whr'] },
  { base: 'nigga', variations: ['nigga', 'n*gga', 'n!gga', 'n1gga', 'niqqa'] },
  { base: 'nigger', variations: ['nigger', 'n*gger', 'n!gger', 'n1gger'] },
  { base: 'faggot', variations: ['faggot', 'f*ggot', 'f@ggot', 'f4ggot', 'fggot'] },
  { base: 'retard', variations: ['retard', 'r*tard', 'r!tard', 'rtard'] },
  { base: 'retarded', variations: ['retarded', 'r*tarded', 'r!tarded'] },
  { base: 'damn', variations: ['damn', 'd*mn', 'd!mn', 'dmn', 'dam'] },
  { base: 'crap', variations: ['crap', 'cr*p', 'cr!p', 'crp'] },
  { base: 'cock', variations: ['cock', 'c*ck', 'c!ck', 'c0ck', 'co.ck', 'cck'] },
  { base: 'motherfucker', variations: ['motherfucker', 'motherfuckers', 'motherf*cker', 'mf', 'mofo'] },
  { base: 'twat', variations: ['twat', 'tw*t', 'tw!t', 'twt'] },
  { base: 'douche', variations: ['douche', 'd*uche', 'd!uche', 'dche'] },
  { base: 'cum', variations: ['cum', 'c*m', 'c.u.m', 'cumm'] },
  { base: 'rape', variations: ['rape', 'r*pe', 'r@pe', 'rap3'] },
  { base: 'rapist', variations: ['rapist', 'r*pist', 'rap1st'] },
  { base: 'anal', variations: ['anal', '4nal', 'a.n.a.l'] },
  { base: 'boobs', variations: ['boobs', 'b00bs', 'bo0bs'] },
  { base: 'tits', variations: ['tits', 't!ts', 't1ts'] },
  { base: 'tit', variations: ['tit', 't!t', 't1t'] },
  { base: 'milf', variations: ['milf', 'm!lf', 'm1lf'] },
  { base: 'sex', variations: ['sex', 's3x', 's*x', 's.e.x'] },
  { base: 'porn', variations: ['porn', 'p*rn', 'p0rn', 'pr0n'] },
  { base: 'nude', variations: ['nude', 'n*de', 'n00d', 'n@de'] },
  { base: 'boner', variations: ['boner', 'b0ner', 'b*ner'] },
  { base: 'orgy', variations: ['orgy', '0rgy', 'org1e'] },
  { base: 'gay', variations: ['gay', 'g@y', 'g4y'] },
  { base: 'lesbian', variations: ['lesbian', 'l*sbian', 'lezbian'] },
  { base: 'thot', variations: ['thot', 'th0t', 'th*t'] },
  { base: 'peen', variations: ['peen', 'p3en', 'p*en'] },
  { base: 'dildo', variations: ['dildo', 'd!ldo', 'd1ldo'] },
  { base: 'suck', variations: ['suck', 's*ck', 'sux'] },
  { base: 'balls', variations: ['balls', 'b@lls', 'b*lls'] },
  { base: 'blowjob', variations: ['blowjob', 'bl0wjob', 'bj', 'b.j.'] },
  { base: 'jerk', variations: ['jerk', 'j3rk', 'j*rk'] },
  { base: 'hoe', variations: ['hoe', 'h*e', 'h0e'] },
  { base: 'kys', variations: ['kys', 'k*y*s', 'k!ys'] },
  { base: 'kill yourself', variations: ['kill yourself', 'kys', 'k!ll yourself'] },
  { base: 'shemale', variations: ['shemale', 'shem@le', 'shem4le'] }
];

// Function to detect swear words in text
function detectSwears(text) {
  const foundSwears = [];
  const lowerText = text.toLowerCase();

  for (const swearGroup of SWEAR_WORDS) {
    let found = false;

    for (const variation of swearGroup.variations) {
      if (found) break;

      // Escape special regex characters
      const escapedVariation = variation.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      // Method 1: Exact word boundaries (normal cases)
      const exactRegex = new RegExp(`\\b${escapedVariation}\\b`, 'gi');
      if (exactRegex.test(lowerText)) {
        foundSwears.push(swearGroup.base);
        found = true;
        break;
      }

      // Method 2: Flexible matching for variations (without word boundaries)
      // This catches cases where word boundaries don't work
      const flexibleRegex = new RegExp(escapedVariation, 'gi');
      if (flexibleRegex.test(lowerText)) {
        foundSwears.push(swearGroup.base);
        found = true;
        break;
      }

      // Method 3: Check for variations with repeated letters
      // This catches "nnnigga", "dickkk", "pppenis", etc.
      if (variation.match(/^[a-z]+$/)) { // Only for pure letter words
        let flexiblePattern = '';
        for (let i = 0; i < variation.length; i++) {
          const char = variation[i];
          flexiblePattern += `${char}+`;
        }

        // Allow up to 3 extra characters before and after
        const extendedRegex = new RegExp(`[a-z]{0,3}${flexiblePattern}[a-z]{0,3}`, 'gi');
        const matches = lowerText.match(extendedRegex);

        if (matches) {
          for (const match of matches) {
            // Verify it's not too long (avoid false positives)
            if (match.length <= variation.length + 6) {
              foundSwears.push(swearGroup.base);
              found = true;
              break;
            }
          }
        }
      }
    }
  }

  return [...new Set(foundSwears)]; // Remove any duplicates
}

module.exports = {
  name: "swear",
  aliases: ['swears'],
  description: `track swear usage in the server`,
  usage: '{guildprefix}swear\n{guildprefix}swear list\n{guildprefix}swear [user]\n{guildprefix}swear leaderboard\n{guildprefix}swear reset',
  run: async(client, message, args) => {

    // Ensure guild data exists in the database
    await ensureGuildData(message.guild.id);

    if (args[0] === 'list') {
      // Show user's own swear list
      try {
        const userData = await guildCache.getUserSwearData(message.guild.id, message.author.id);
        
        if (!userData || userData.swears.length === 0) {
          return embeds.warn(message, 'You have no recorded swears in this server');
        }

        // Filter out words with 0 count and sort by count (highest first)
        const usedSwears = userData.swears.filter(swear => swear.count > 0);
        const sortedSwears = usedSwears.sort((a, b) => b.count - a.count);

        if (sortedSwears.length === 0) {
          return embeds.warn(message, 'You have no recorded swears in this server');
        }

        // Calculate actual total from individual word counts
        const calculatedTotal = usedSwears.reduce((total, swear) => total + swear.count, 0);

        // Format page function for pagination
        const formatPage = (pageSwears, currentPage, totalPages) => {
          const swearsList = pageSwears.map((swear, index) => {
            const number = ((currentPage - 1) * 10 + index + 1).toString().padStart(2, '0');
            return `\`${number}\` **${swear.word}** - ${swear.count}`;
          }).join('\n');

          // Use calculated total instead of stored total to fix discrepancy
          const footerText = `You have sweared ${userData.todaySwears} times today • Total: ${calculatedTotal} • Page ${currentPage}/${totalPages}`;

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle(`${message.author.username}'s Swear List`)
            .setDescription(swearsList)
            .setFooter({ text: footerText });
        };

        // Use pagination system with 10 items per page
        await createPagination(message, sortedSwears, formatPage, 10, 'Swear List');
      } catch (error) {
        console.error('Error getting swear list:', error);
        return embeds.deny(message, 'Failed to retrieve swear list');
      }

    } else if (args[0] === 'leaderboard' || args[0] === 'lb') {
      // Show server swear leaderboard
      try {
        const leaderboard = await guildCache.getSwearLeaderboard(message.guild.id);
        
        if (leaderboard.length === 0) {
          return embeds.warn(message, 'No swear data found for this server');
        }

        const userPosition = await guildCache.getUserSwearPosition(message.guild.id, message.author.id);

        // Format page function for pagination
        const formatPage = (pageUsers, currentPage, totalPages) => {
          const startIndex = (currentPage - 1) * 10;

          const leaderboardList = pageUsers.map((userData, index) => {
            const position = startIndex + index + 1;
            const member = message.guild.members.cache.get(userData.userId);
            let username = 'Unknown User';

            if (member) {
              username = member.displayName;
            } else {
              // Try to get user from client cache if not in guild
              const user = client.users.cache.get(userData.userId);
              if (user) {
                username = user.username;
              }
            }

            return `\`${position.toString().padStart(2, '0')}\` **${username}** - ${userData.calculatedTotal} swears`;
          }).join('\n');

          const footerText = userPosition 
            ? `Your position: #${userPosition} • Page ${currentPage}/${totalPages}`
            : `Page ${currentPage}/${totalPages}`;

          return new EmbedBuilder()
            .setColor(config.colors.embed)
            .setTitle('Swear Leaderboard')
            .setDescription(leaderboardList)
            .setFooter({ text: footerText });
        };

        // Use pagination system with 10 items per page
        await createPagination(message, leaderboard, formatPage, 10, 'Swear Leaderboard');
      } catch (error) {
        console.error('Error getting swear leaderboard:', error);
        return embeds.deny(message, 'Failed to retrieve swear leaderboard');
      }

    } else if (args[0] === 'reset') {
      // Reset all swear data (admin only)
      if (!hasDiscordPermission(message, PermissionFlagsBits.Administrator, 'Administrator')) return;

      try {
        const swearData = await guildCache.getSwearTracker(message.guild.id);

        if (swearData.length === 0) {
          return embeds.warn(message, 'No swear data to reset');
        }

        // Show confirmation dialog
        await createConfirmation(
          message,
          'Are you sure you want to **reset all swear data** for this server?',
          async (interaction) => {
            // On approve: clear all swear data
            await guildCache.clearAllSwearData(message.guild.id);

            // Edit the confirmation embed to show success message
            const successEmbed = embeds.createSuccess(message, 'Reset all swear data for this server');

            await interaction.editReply({ embeds: [successEmbed], components: [] });
          }
        );
      } catch (error) {
        console.error('Error resetting swear data:', error);
        return embeds.deny(message, 'Failed to reset swear data');
      }

    } else if (args[0]) {
      // Handle "swears [user]" format - check if first arg is a user
      const userResult = await findUser(message.guild, args[0], client);

      if (userResult.found) {
        // Show specific user's swear stats
        const targetUser = userResult.user;
        const userId = targetUser.id || targetUser.user?.id;

        try {
          const userData = await guildCache.getUserSwearData(message.guild.id, userId);

          if (!userData || !userData.swears || userData.swears.length === 0) {
            const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
            return embeds.info(message, `**${username}** has no recorded swears in this server`);
          }

          // Calculate actual total from individual word counts
          const calculatedTotal = userData.swears.reduce((total, swear) => total + swear.count, 0);

          if (calculatedTotal === 0) {
            const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
            return embeds.info(message, `**${username}** has no recorded swears in this server`);
          }

          const username = targetUser.displayName || targetUser.username || targetUser.user?.username;
          return embeds.info(message, `**${username}** has sweared **${calculatedTotal}** times`);
        } catch (error) {
          console.error('Error getting user swear data:', error);
          return embeds.deny(message, 'Failed to retrieve user swear data');
        }
      } else {
        // If not a user, show help
        return runHelpCommand(message, 'swear');
      }

    } else {
      // Show user's own swear stats
      try {
        const userData = await guildCache.getUserSwearData(message.guild.id, message.author.id);

        if (!userData || !userData.swears || userData.swears.length === 0) {
          return embeds.info(message, 'You have no recorded swears in this server');
        }

        // Calculate actual total from individual word counts
        const calculatedTotal = userData.swears.reduce((total, swear) => total + swear.count, 0);

        if (calculatedTotal === 0) {
          return embeds.info(message, 'You have no recorded swears in this server');
        }

        return embeds.info(message, `You have sweared **${calculatedTotal}** times`);
      } catch (error) {
        console.error('Error getting swear data:', error);
        return embeds.deny(message, 'Failed to retrieve swear data');
      }
    }
  },

  // Export the detectSwears function for use in message events
  detectSwears
}
